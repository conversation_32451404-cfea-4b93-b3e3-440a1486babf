# UE5.6游戏项目开发守则

## 项目概述

### 核心目标
- 基于UE5.6最新功能实现大世界地图生成和性能优化系统
- 严格遵循ECS架构（Mass Entity系统）和数据驱动设计
- 集成ComputeFramework、FStreamableManager等UE5.6原生组件
- 使用现代化C++17/20特性和单一职责原则

### 技术栈要求
- **引擎版本**: Unreal Engine 5.6
- **C++标准**: C++17/20 (UE5.6支持的最新标准)
- **核心插件**: MassGameplay, ComputeFramework, ModelingToolsEditorMode
- **核心模块**: Engine, CoreUObject, AssetRegistry, MassEntity, MassSpawner

## UE5.6功能使用强制规范

### 智能指针使用规范

#### ✅ 必须使用
```cpp
// UE对象指针 - UE5.6推荐
TObjectPtr<UObject> MyObject;
TWeakObjectPtr<AActor> WeakActor;

// 共享指针
TSharedPtr<FMyClass> SharedData;
TUniquePtr<FMyClass> UniqueData;
```

#### ❌ 严格禁止
```cpp
// 原始指针 - 违反项目规范
UObject* RawPointer;  // 禁止
AActor* ActorPtr;     // 禁止

// 手动内存管理 - 违反UE5.6最佳实践
FMyClass* ManualPtr = new FMyClass();  // 禁止
```

## 代码重复检查和清理规范

### 🔍 实现前强制检查流程
1. **UE5.6 API搜索**：使用`resolve-library-id`和`get-library-docs`工具检查UE引擎是否已有相同功能
2. **项目代码搜索**：使用`codebase-retrieval`工具搜索项目中是否存在类似实现
3. **Mass Entity检查**：优先检查Mass Components和Processors是否已有相关功能
4. **Performance系统检查**：检查`Source/Game/Public/Performance`目录下是否已有相关功能

### 🧹 代码清理强制要求
```cpp
// ❌ 必须删除的废弃代码示例
// 【已删除】GetObjectPoolSize - 已被GetActiveObjectsCount替代  // 删除此类注释

// ❌ 必须删除的重复实现
class FUnifiedObjectPool { /* 重复实现，必须删除 */ };
class FTieredObjectPool { /* 保留更优实现 */ };

// ✅ 保留的优化实现
template<typename T>
class FTieredObjectPool {
    // 三层存储：Hot/Warm/Cold
};
```

### 📝 增量更新规范
```cpp
// ✅ 正确的增量更新方式
void ExistingFunction(int32 NewParam = 0) // 添加默认参数保持兼容性
{
    // 保留原有逻辑
    OriginalLogic();

    // 添加新功能
    if (NewParam > 0) {
        NewFeatureLogic(NewParam);
    }
}

// ❌ 禁止完全重写
void ExistingFunction() // 不要删除原有实现重新写
{
    // 完全新的实现 - 禁止这样做
}
```

### UE引擎功能检查规范

#### 🔍 代码实现前必须检查
- **搜索UE5.6 API文档**：确认引擎是否已有相同功能
- **检查Mass Entity系统**：优先使用Mass Components和Processors
- **检查ComputeFramework**：GPU计算任务必须使用ComputeFramework
- **检查现有Subsystem**：继承现有UWorldSubsystem架构

## ECS架构和数据驱动设计强制规范

### Mass Entity系统强制使用

#### ✅ 必须使用的ECS模式
```cpp
// Mass Component - 数据存储
USTRUCT()
struct GAME_API FPerformanceComponent : public FMassFragment
{
    GENERATED_BODY()

    UPROPERTY()
    float OptimizationLevel = 1.0f;

    UPROPERTY()
    int32 CacheHitCount = 0;
};

// Mass Processor - 逻辑处理
UCLASS()
class GAME_API UPerformanceProcessor : public UMassProcessor
{
    GENERATED_BODY()

protected:
    virtual void ConfigureQueries(const TSharedRef<FMassEntityManager>& EntityManager) override;
    virtual void Execute(FMassEntityManager& EntityManager, FMassExecutionContext& Context) override;

private:
    FMassEntityQuery EntityQuery;
};
```

#### ❌ 禁止的传统OOP模式
```cpp
// 禁止创建传统的游戏对象类
class APerformanceActor : public AActor // 违反ECS架构
{
    // 不要在Actor中混合数据和逻辑
};
```

### 数据驱动设计强制要求

#### ✅ 必须使用UDataAsset配置
```cpp
// 配置数据资产
UCLASS(BlueprintType)
class GAME_API UPerformanceConfigDataAsset : public UDataAsset
{
    GENERATED_BODY()

public:
    UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Object Pool")
    int32 MaxPoolSize = 1000;

    UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Cache")
    float CacheExpirationTime = 300.0f;

    UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "GPU Compute")
    bool bEnableGPUCompute = true;
};
```

#### ❌ 禁止硬编码配置
```cpp
// 禁止在代码中硬编码配置值
const int32 POOL_SIZE = 1000; // 违反数据驱动原则
const float CACHE_TIME = 300.0f; // 必须移到DataAsset中
```

## Performance系统强制使用规范

### 🎯 对象池缓存系统强制要求

#### ✅ 必须使用Source/Game/Public/Performance中的函数
```cpp
// 获取性能优化器实例
UPerformanceOptimizer* Optimizer = GetWorld()->GetSubsystem<UPerformanceOptimizer>();

// 使用统一对象池获取对象
TSharedPtr<FMapCell> Cell = Optimizer->GetPooledMapCell();
TSharedPtr<AActor> Actor = Optimizer->GetTieredPooledActor();

// 返回对象到池
Optimizer->ReturnPooledMapCell(Cell);
Optimizer->ReturnTieredPooledActor(Actor, 1); // 指定层级

// 使用缓存系统
FString CachedData = Optimizer->GetCachedData(Key);
Optimizer->SetCachedData(Key, Data, ExpirationTime);
```

#### ❌ 严格禁止自定义对象池
```cpp
// 禁止创建自定义对象池
class MyCustomPool { /* 违反项目规范 */ };

// 禁止直接new/delete
FMapCell* Cell = new FMapCell(); // 必须使用对象池
delete Cell; // 必须使用对象池

// 禁止绕过Performance系统
TArray<FMapCell> DirectArray; // 大量对象必须使用对象池
```

### 🖥️ GPU计算强制规范

#### ✅ 必须使用ComputeFramework
```cpp
// 获取GPU计算组件
UComputeGraphComponent* ComputeComponent = Optimizer->GetGPUComputeComponent();

// 执行GPU计算
if (ComputeComponent && Optimizer->IsGPUComputeEnabled()) {
    ComputeComponent->QueueExecute();
}
```

#### ❌ 禁止自定义GPU计算
```cpp
// 禁止直接使用RHI进行GPU计算
FRHICommandListImmediate& RHICmdList = FRHICommandListExecutor::GetImmediateCommandList(); // 禁止
```

## 单一职责原则强制规范

### 🎯 类职责划分强制要求

#### ✅ 正确的单一职责设计
```cpp
// 每个Manager只负责一个领域
UCLASS()
class GAME_API UObjectPoolManager : public UWorldSubsystem
{
    // 只负责对象池管理
};

UCLASS()
class GAME_API UCacheManager : public UWorldSubsystem
{
    // 只负责缓存管理
};

UCLASS()
class GAME_API UGPUComputeManager : public UWorldSubsystem
{
    // 只负责GPU计算管理
};
```

#### ❌ 违反单一职责的设计
```cpp
// 禁止一个类承担多个职责
UCLASS()
class UEverythingManager : public UWorldSubsystem
{
    // 对象池 + 缓存 + GPU计算 + 网络 + AI - 违反单一职责原则
};
```

### 🔧 函数职责划分

#### ✅ 函数单一职责
```cpp
// 每个函数只做一件事
void InitializeObjectPool();
void CleanupExpiredCache();
void ExecuteGPUComputation();

// 复合操作通过组合实现
void InitializePerformanceSystem()
{
    InitializeObjectPool();
    InitializeCacheSystem();
    InitializeGPUCompute();
}
```

#### ❌ 违反单一职责的函数
```cpp
// 禁止一个函数做多件不相关的事
void DoEverything() // 违反单一职责
{
    InitializePool();
    LoadAssets();
    ProcessAI();
    RenderGraphics();
}
```

## UE5.6最新功能强制使用规范

### 🚀 必须优先使用的UE5.6功能

#### ✅ Instanced Actors（大量对象渲染优化）
```cpp
// 使用Instanced Actors替代传统Actor
UCLASS()
class GAME_API UInstancedActorManager : public UWorldSubsystem
{
public:
    // 创建实例化Actor
    void CreateInstancedActors(TSubclassOf<AActor> ActorClass, const TArray<FTransform>& Transforms);
};
```

#### ✅ 最新异步API
```cpp
// 使用AsyncTask替代过时的Async
AsyncTask(ENamedThreads::AnyBackgroundThreadNormalTask, []() {
    // 后台任务
});

// 使用FStreamableManager进行异步资源加载
StreamableManager->RequestAsyncLoad(AssetPaths, FStreamableDelegate::CreateLambda([this]() {
    // 加载完成回调
}));
```

#### ✅ ComputeFramework深度集成
```cpp
// 使用ComputeFramework进行GPU计算
UCLASS()
class GAME_API UMyComputeGraph : public UComputeGraph
{
    // GPU计算图定义
};
```

#### ❌ 禁止使用过时功能
```cpp
// 过时的异步API
Async(EAsyncExecution::ThreadPool, []() {}); // 禁止使用

// 过时的资源加载方式
UObject* Asset = LoadObject<UObject>(nullptr, TEXT("/Path/To/Asset")); // 禁止同步加载
```

// 3. 項目特定頭文件
#include "LevelGen/MapUtil.h"

// 4. 現代化C++標準庫 (謹慎使用)
#include <memory>
#include <optional>

// 5. 生成的頭文件 (必須最後)
#include "ClassName.generated.h"
```

### 多文件連動修改規則

#### 修改PerformanceOptimizer.h時必須同步修改
- `Source/Game/Private/Performance/PerformanceOptimizer.cpp`
- 所有相關的實現文件 (AsyncChunkLoader.cpp, AdvancedCompression.cpp等)

#### 修改枚舉定義時必須同步檢查
- 所有使用該枚舉的.cpp文件
- 所有相關的switch語句
- 所有相關的日誌輸出

#### 添加新的成員變量時必須
- 在構造函數中初始化
- 在Initialize()函數中設置
- 在Shutdown()函數中清理

## 性能優化專用規範

### 日誌標籤規範

#### ✅ 必須使用中文標籤
```cpp
UE_LOG(LogTemp, Log, TEXT("【UE5.6優化】功能初始化完成"));
UE_LOG(LogTemp, VeryVerbose, TEXT("【異步加載】塊加載完成: (%d, %d)"), X, Y);
UE_LOG(LogTemp, Warning, TEXT("【內存壓力】內存使用率過高: %.1f%%"), Pressure);
```

### 性能統計規範

#### ✅ 必須使用UE5.6性能工具
```cpp
// 使用FPlatformMemory獲取內存統計
const FPlatformMemoryStats MemStats = FPlatformMemory::GetStats();

// 使用FDateTime替代FPlatformTime::Seconds()
const FDateTime CurrentTime = FDateTime::UtcNow();

// 使用原子操作進行線程安全統計
std::atomic<int32> Counter{0};
Counter.fetch_add(1, std::memory_order_relaxed);
```

### 緩存系統規範

#### ✅ 必須使用UE5.6緩存最佳實踐
```cpp
// 使用FStreamableManager進行資源緩存
StreamableManager->RequestAsyncLoad(AssetPaths, Delegate);

// 使用IAssetRegistry進行資源查詢
AssetRegistry->GetAssetsByClass(UStaticMesh::StaticClass(), AssetData);
```

## 錯誤處理規範

### ✅ 必須使用UE5.6錯誤處理機制
```cpp
// 使用check宏進行斷言
check(IsValid(MyObject));
checkf(Condition, TEXT("錯誤信息: %s"), *ErrorString);

// 使用ensure進行非致命檢查
if (!ensure(IsValid(MyObject)))
{
    return false;
}

// 使用UE_LOG進行錯誤記錄
UE_LOG(LogTemp, Error, TEXT("【錯誤】操作失敗: %s"), *ErrorMessage);
```

### ❌ 禁止使用標準C++異常
```cpp
// 禁止使用try/catch
try {
    // 代碼
} catch (...) {
    // 處理
}

// 禁止拋出異常
throw std::runtime_error("錯誤");
```

## 禁止事項

### 絕對禁止的編程實踐

#### ❌ 內存管理禁止事項
- 使用原始指針管理UE對象
- 手動調用new/delete
- 忽略內存泄漏檢查
- 在析構函數中訪問UE對象

#### ❌ 異步編程禁止事項
- 在非主線程直接操作UE對象
- 忽略線程安全問題
- 使用過時的異步API
- 阻塞主線程進行長時間操作

#### ❌ 性能優化禁止事項
- 在Tick函數中進行重型計算
- 忽略LOD系統
- 過度使用反射
- 忽略內存對齊

#### ❌ 代码结构禁止事项
- 在头文件中包含大型实现
- 循环依赖
- 违反单一职责原则
- 重复实现UE5.6已有功能

## 项目逻辑一致性和设计意图保证

### 🎯 设计意图强制检查清单

#### ✅ 每次代码修改必须验证
1. **ECS架构一致性**：所有游戏逻辑必须通过Mass Entity系统实现
2. **数据驱动设计**：所有配置必须通过UDataAsset管理
3. **单一职责原则**：每个类/函数只负责一个明确的职责
4. **性能优化目标**：所有修改必须考虑性能影响
5. **UE5.6功能优先**：优先使用引擎最新功能而非自定义实现

#### ✅ 逻辑一致性验证
```cpp
// 正确的逻辑流程
void ProcessGameLogic()
{
    // 1. 通过Mass Entity系统获取数据
    FMassEntityQuery Query;
    Query.ForEachEntityChunk(EntityManager, Context, [](FMassExecutionContext& Context) {
        // 2. 批量处理实体数据
        ProcessEntitiesInBatch(Context);
    });

    // 3. 使用Performance系统优化
    UPerformanceOptimizer* Optimizer = GetPerformanceOptimizer();
    Optimizer->OptimizeCurrentFrame();
}
```

#### ❌ 违反设计意图的实现
```cpp
// 违反ECS架构
void ProcessGameLogic()
{
    // 直接操作Actor - 违反ECS设计意图
    for (AActor* Actor : AllActors) {
        Actor->ProcessLogic(); // 错误：应该使用Mass Entity
    }
}
```

### 🔧 AI Agent决策优先级
1. **UE5.6原生功能** > 自定义实现
2. **Mass Entity系统** > 传统Actor系统
3. **Performance系统对象池** > 直接内存分配
4. **数据驱动配置** > 硬编码值
5. **增量更新** > 完全重写
6. **单一职责设计** > 大而全的类

### ⚠️ 关键文件同步修改规范
- **修改PerformanceOptimizer**时，必须同步更新相关Manager子系统
- **添加Mass Component**时，必须创建对应的Mass Processor
- **修改DataAsset结构**时，必须更新所有使用该配置的代码
- **添加新的Subsystem**时，必须在Game.uproject中声明依赖
- 全局變量 (除非必要)
- 硬編碼魔法數字

### 特定於本項目的禁止事項

#### ❌ 性能優化器特定禁止
- 修改核心優化邏輯時不更新性能統計
- 添加新的緩存級別時不更新清理邏輯
- 修改異步加載時不考慮優先級系統
- 忽略GPU/CPU計算的回退機制

## AI決策規範

### 模糊情況處理優先級
1. **優先使用UE5.6原生功能** - 總是優先選擇UE內置解決方案
2. **保持向後兼容** - 修改時確保不破壞現有功能
3. **性能優先** - 在功能和性能之間選擇性能
4. **線程安全優先** - 多線程環境下優先考慮安全性

### 決策樹
```
需要添加新功能？
├─ 是否有UE5.6原生支持？
│  ├─ 是 → 使用UE原生功能
│  └─ 否 → 檢查是否可以擴展現有系統
├─ 是否影響性能？
│  ├─ 是 → 添加性能監控和優化
│  └─ 否 → 正常實現
└─ 是否需要多線程？
   ├─ 是 → 使用UE異步框架
   └─ 否 → 在主線程實現
```

## 關鍵文件交互規範

### 核心文件依賴關係

#### PerformanceOptimizer.h 修改時必須檢查
- `PerformanceOptimizer.cpp` - 主實現文件
- `AsyncChunkLoader.cpp` - 異步加載實現
- `AdvancedCompression.cpp` - 壓縮算法實現
- `MemoryMappedCache.cpp` - 內存映射實現

#### 枚舉修改連動規則
```cpp
// 修改 ELODLevel 時必須同步更新
- CalculateLODLevel() 函數中的所有分支
- ApplyLODToChunk() 函數中的處理邏輯
- SimplifyMapData() 函數中的LOD處理
- 所有相關的日誌輸出和統計
```

#### 結構體修改連動規則
```cpp
// 修改 FMapChunk 時必須同步更新
- 所有使用該結構體的序列化代碼
- 相關的壓縮/解壓縮邏輯
- 緩存系統的存儲格式
- 網絡同步相關代碼（如果存在）
```

### 模塊間接口規範

#### 異步加載模塊接口
- 必須通過 `FAsyncChunkLoader` 類進行所有異步操作
- 禁止直接調用底層異步API
- 必須使用優先級隊列系統

#### 緩存模塊接口
- 必須通過 `CacheMapData()` 和 `GetCachedMapData()` 進行緩存操作
- 禁止直接操作 `CacheEntries` 容器
- 必須考慮緩存級別和壓縮策略

#### 對象池模塊接口
- 必須通過 `GetPooledObject<T>()` 和 `ReturnPooledObject<T>()` 操作
- 禁止直接操作池容器
- 必須考慮分層池和統一池的回退機制

## 版本控制和更新規範

### 代碼更新時必須執行的檢查清單

#### ✅ 功能更新檢查清單
1. 更新相關的中文註釋標籤
2. 更新性能統計相關代碼
3. 檢查是否需要更新初始化/清理邏輯
4. 驗證線程安全性
5. 更新相關的錯誤處理
6. 檢查內存管理是否正確

#### ✅ 接口更新檢查清單
1. 檢查所有調用該接口的代碼
2. 更新相關的模板特化
3. 檢查藍圖接口的兼容性
4. 更新相關的序列化代碼
5. 檢查網絡同步影響（如果適用）

#### ✅ 性能優化更新檢查清單
1. 添加性能監控點
2. 更新相關統計信息
3. 檢查是否需要新的優化參數
4. 驗證多線程性能影響
5. 測試內存使用變化

### UE5.6特定更新要求

#### 引擎API更新適配
- 優先使用UE5.6新增的API替代舊版本
- 檢查廢棄API的替代方案
- 更新頭文件包含以使用最新模塊

#### 現代化C++特性採用
- 逐步將舊代碼重構為現代化C++
- 使用auto、constexpr等關鍵字提升代碼質量
- 採用RAII模式改善資源管理

## 測試和驗證規範

### 必須進行的測試類型

#### ✅ 性能測試要求
```cpp
// 必須測試的性能指標
- 內存使用量變化
- CPU使用率影響
- 加載時間變化
- 緩存命中率
- 對象池效率
```

#### ✅ 穩定性測試要求
```cpp
// 必須測試的穩定性場景
- 長時間運行測試
- 內存壓力測試
- 多線程併發測試
- 異常情況恢復測試
```

#### ✅ 兼容性測試要求
```cpp
// 必須測試的兼容性
- 不同LOD級別切換
- 不同壓縮算法切換
- 不同緩存策略切換
- 異步/同步模式切換
```

這個規範文件現在為AI Agent提供了完整的UE5.6項目開發指導，包含了具體的代碼示例、禁止事項、決策規則和文件交互規範。所有規則都使用命令式語言，專注於為AI提供明確的操作指導。
